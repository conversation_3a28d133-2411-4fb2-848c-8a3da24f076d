<template>
  <view class="rwk-page" :style="mix_diyStyle">
    <!-- 背景层 -->
    <view class="bg">
      <view class="bg-gradient" />
      <!-- 简易粒子光效 -->
      <view class="particles">
        <view v-for="i in 32" :key="i" class="particle" />
      </view>
    </view>

    <view class="container">
      <!-- 标题/提示 -->
      <view class="title">人物卡抽取</view>

      <!-- 牌堆区域 -->
      <view class="deck-wrap" :class="{ expanded: deckExpanded }">
        <view class="deck" :class="{ 'tilt-top': !deckExpanded, 'tilt-front': deckExpanded }" :style="deckStyle">
          <!-- 顶视堆叠：使用位移+阴影制造厚度 -->
          <view
            v-for="(n, idx) in deckCount"
            :key="idx"
            class="card-back-item"
            :style="getStackStyle(idx)"
          >
            <image class="card-img" :src="cardBackUrl" mode="aspectFill" />
          </view>
        </view>
      </view>

      <!-- 选中展示的三张牌（覆盖在堆叠上方） -->
      <view class="selected-layer">
        <view
          v-for="(card, i) in selectedCards"
          :key="i"
          class="pick-card"
          :class="{ show: drawn[i], flipped: flipped[i] }"
          :style="getPickedStyle(i)"
        >
          <view class="card-3d">
            <!-- 背面 -->
            <view class="face back">
              <image class="card-img" :src="cardBackUrl" mode="aspectFill" />
            </view>
            <!-- 正面（暂用炫彩渐变+占位内容） -->
            <view class="face front">
              <view class="front-content">
                <view class="front-title">角色卡</view>
                <view class="front-sub">ROLE CARD</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作区 -->
      <view class="action" v-if="stage === 'idle'" @tap.stop="startDraw" @click.stop="startDraw">
        <button class="btn primary" :disabled="isDrawing" @tap.stop="startDraw" @click.stop="startDraw">抽取人物卡</button>
      </view>

      <view class="action" v-else-if="stage === 'showing'">
        <button class="btn ghost" @click="onMore">更多解读</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // UI
      deckCount: 22,
      cardBackUrl: 'http://sg-mall-prod.oss-accelerate.aliyuncs.com/sg/card_bg.png',

      // 动画状态机: idle -> expanding -> selecting -> showing
      stage: 'idle',
      isDrawing: false,
      deckExpanded: false,

      // 三张被抽中的牌及其状态
      selectedCards: [0, 1, 2],
      drawn: [false, false, false],     // 是否已从牌堆抽出并出现
      arrived: [false, false, false],   // 是否已抵达目标位置
      flipped: [false, false, false],   // 是否已翻转
    }
  },
  computed: {
    // 与项目风格系统对齐（若项目已注入 mix_diyStyle）
    mix_diyStyle() {
      return ''
    },
    deckStyle() {
      // 可根据业务需要在此做响应式适配（此处先返回空）
      return ''
    }
  },
  methods: {
    getStackStyle(idx) {
      // 更接近正视：以Y方向为主的位移，少量X偏移与微小旋转，叠加轻度Z抬升
      const yGap = 6 // 每张卡堆叠间距
      const xWobble = (idx % 3 - 1) * 2 // -2,0,2 rpx 轻微左右摆动
      const rot = (idx % 7 - 3) * 0.6 // -1.8 ~ 2.4 度
      const zLift = Math.min(idx * 0.6, 8) // 轻微Z抬升增强厚度
      return `transform: translate(${xWobble}rpx, ${idx * yGap}rpx) translateZ(${zLift}rpx) rotate(${rot}deg); z-index:${idx};`
    },
    getPickedStyle(i) {
      // 初始位置：从牌堆顶部中心略微上方开始
      const deckX = 0, deckY = -40
      const positions = [
        { x: -190, y: -320, r: -8, s: 0.9 },
        { x: 0, y: -320, r: 0, s: 0.96 },
        { x: 190, y: -320, r: 8, s: 0.9 },
      ]
      const p = positions[i]

      // 未抽出：隐藏
      if (!this.drawn[i]) {
        return `transform: translate3d(${deckX}rpx, ${deckY}rpx, 0) scale(0.7) rotate(0deg); opacity:0;`
      }
      // 抽出后但未到位：从牌堆向目标位移动
      if (this.drawn[i] && !this.arrived[i]) {
        return `transform: translate3d(${p.x * 0.3}rpx, ${p.y * 0.4}rpx, 0) scale(0.82) rotate(${p.r * 0.6}deg); opacity:1;`
      }
      // 到位：
      return `transform: translate3d(${p.x}rpx, ${p.y}rpx, 0) scale(${p.s}) rotate(${p.r}deg); opacity:1;`
    },

    startDraw() {
      console.log('[rwk] startDraw tapped')
      try { uni.showToast({ title: '开始抽取', icon: 'none', duration: 600 }) } catch(e) {}
      if (this.isDrawing) return
      this.isDrawing = true
      this.stage = 'expanding'

      // 1) 让牌堆从顶视转为正视
      this.deckExpanded = true

      // 2) 选择三张（动画用）并重置状态
      const idxs = this.randomThree(this.deckCount)
      this.selectedCards = idxs
      this.drawn = [false, false, false]
      this.arrived = [false, false, false]
      this.flipped = [false, false, false]

      // 3) 依次抽取三张
      const stepDelay = 380 // 相邻两张启动间隔
      const flyDuration = 420
      const flipDelay = 220

      const drawOne = (i) => {
        // a) 从牌堆“抽出”（出现并开始移动）
        this.$set ? this.$set(this.drawn, i, true) : (this.drawn[i] = true)
        // b) 到位
        setTimeout(() => {
          this.$set ? this.$set(this.arrived, i, true) : (this.arrived[i] = true)
          // c) 翻转
          setTimeout(() => {
            this.$set ? this.$set(this.flipped, i, true) : (this.flipped[i] = true)
            // 最后一张翻完收尾
            if (i === 2) {
              this.stage = 'showing'
              this.isDrawing = false
              this.firePulse()
            }
          }, flipDelay)
        }, flyDuration)
      }

      setTimeout(() => drawOne(0), 50)
      setTimeout(() => drawOne(1), 50 + stepDelay)
      setTimeout(() => drawOne(2), 50 + stepDelay * 2)
    },

    randomThree(n) {
      const arr = Array.from({ length: n }, (_, i) => i)
      arr.sort(() => Math.random() - 0.5)
      const picked = arr.slice(0, 3)
      // 保持顺序：左-中-右
      picked.sort((a, b) => a - b)
      return picked
    },

    onMore() {
      // TODO: 后续接入跳转或弹窗
      this.$api && this.$api.msg ? this.$api.msg('更多解读（待接入）') : uni.showToast({ title: '更多解读（待接入）', icon: 'none' })
    },

    firePulse() {
      // 添加一个页面级脉冲高光效果
      const el = this.$el.querySelector('.bg-gradient')
      if (!el) return
      el.classList.add('pulse-once')
      setTimeout(() => el.classList.remove('pulse-once'), 800)
    },
  },
}
</script>

<style lang="scss" scoped>
.rwk-page {
  min-height: 100vh;
  width: 100%;
  background: #0b0d17;
  overflow: hidden;
  color: #e7ecff;
}

.bg {
  position: fixed;
  inset: 0;
  z-index: 0;
  pointer-events: none;

  .bg-gradient {
    position: absolute;
    inset: -20%;
    background:
      radial-gradient(60% 40% at 50% 10%, rgba(60,120,255,0.18), transparent 60%),
      radial-gradient(50% 30% at 10% 80%, rgba(255,60,120,0.18), transparent 60%),
      radial-gradient(40% 50% at 85% 70%, rgba(255,200,60,0.16), transparent 60%),
      linear-gradient(180deg, #0c0f22 0%, #0b0d17 60%, #090b12 100%);
    filter: saturate(120%);
    transition: filter .4s ease, transform .6s ease, opacity .4s ease;

    &.pulse-once { filter: brightness(1.2) saturate(1.3); }
  }

  .particles {
    position: absolute; inset: 0;
    overflow: hidden;

    .particle {
      position: absolute;
      width: 6rpx; height: 6rpx; border-radius: 999px;
      background: radial-gradient(circle at 30% 30%, #fff, rgba(255,255,255,0.1) 60%);
      box-shadow: 0 0 12rpx 4rpx rgba(120,170,255,0.35);
      animation: twinkle 3.2s ease-in-out infinite;
    }

    /* 随机散布 */
    .particle:nth-child(odd) { animation-duration: 2.6s; }
    .particle:nth-child(3n) { animation-duration: 3.8s; }
    .particle:nth-child(5n) { animation-duration: 4.6s; }
    @for $i from 1 through 32 {
      .particle:nth-child(#{$i}) {
        top: random(100) * 1%;
        left: random(100) * 1%;
        opacity: (40 + random(60)) / 100;
      }
    }
  }
}

.container {
  position: relative;
  z-index: 1;
  padding: 48rpx 32rpx 120rpx;
}

.title { font-size: 36rpx; opacity: .8; margin-bottom: 24rpx; letter-spacing: 4rpx; }

/* 牌堆 */
.deck-wrap {
  height: 680rpx;
  perspective: 1400rpx;
  display: flex; align-items: center; justify-content: center;
  transition: transform .6s cubic-bezier(.2,.8,.2,1);
  pointer-events: none; /* 不拦截点击，让按钮可点 */
}
.deck {
  position: relative;
  width: 340rpx; height: 520rpx; /* 竖版长卡片比例 */
  transform-style: preserve-3d;
  transition: transform .6s cubic-bezier(.2,.8,.2,1), width .6s, height .6s;

  /* 更接近正视图 */
  &.tilt-top { transform: rotateX(12deg) rotateZ(-1deg) translateZ(10rpx); }
  &.tilt-front { transform: rotateX(0deg) rotateZ(0deg) translateZ(16rpx); width: 380rpx; height: 560rpx; }
}
.card-back-item {
  position: absolute; top: 0; left: 0;
  width: 340rpx; height: 520rpx; border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 46rpx rgba(0,0,0,.5), 0 2rpx 0 rgba(255,255,255,.04) inset;
}
.card-img { width: 100%; height: 100%; display: block; }

/* 选中的三张牌 */
.selected-layer {
  position: relative; z-index: 3;
  height: 560rpx; margin-top: -420rpx; /* 覆盖牌堆及其上方区域，使起点在牌堆 */
  pointer-events: none; /* 避免在 idle 阶段挡住按钮点击 */
}
.pick-card {
  position: absolute; left: 50%; top: 0;
  width: 340rpx; height: 520rpx; margin-left: -170rpx; border-radius: 24rpx;
  transform-origin: center center;
  transition: transform .6s cubic-bezier(.2,.8,.2,1), opacity .4s;
  opacity: 0;
  filter: drop-shadow(0 18rpx 40rpx rgba(0,0,0,.45));

  &.show { opacity: 1; }

  .card-3d {
    width: 100%; height: 100%; position: relative;
    transform-style: preserve-3d;
    transition: transform .7s cubic-bezier(.2,.8,.2,1);
  }
  &.flipped .card-3d { transform: rotateY(180deg); }

  .face {
    position: absolute; inset: 0; border-radius: 22rpx; overflow: hidden;
    backface-visibility: hidden; -webkit-backface-visibility: hidden;
  }
  .back { transform: rotateY(0deg); background: #111; }
  .front { transform: rotateY(180deg); background: linear-gradient(135deg, #2f2f49 0%, #181828 45%, #1b2238 100%); }

  .front-content {
    position: absolute; inset: 0; display: flex; flex-direction: column; align-items: center; justify-content: center;
    color: #d7e1ff;
    text-align: center;

    /* 炫彩边框与镭射扫光 */
    &::before {
      content: '';
      position: absolute; inset: -2rpx; border-radius: 24rpx;
      padding: 2rpx;
      background: linear-gradient(120deg, rgba(100,200,255,.7), rgba(215,120,255,.7), rgba(255,200,120,.7));
      -webkit-mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
      -webkit-mask-composite: xor; mask-composite: exclude;
      animation: borderFlow 2.4s linear infinite;
    }
    &::after {
      content: '';
      position: absolute; inset: 0; border-radius: 22rpx;
      background: linear-gradient(75deg, rgba(255,255,255,.08), rgba(255,255,255,0) 35%);
      mix-blend-mode: screen;
      animation: holoSweep 2.6s ease-in-out infinite;
    }

    .front-title { font-size: 40rpx; letter-spacing: 6rpx; margin-bottom: 10rpx; }
    .front-sub { font-size: 22rpx; opacity: .7; letter-spacing: 4rpx; }
  }
}

/* 操作按钮 */
.action { margin-top: 40rpx; display: flex; justify-content: center; position: relative; z-index: 10; pointer-events: auto; }
.btn {
  min-width: 420rpx; height: 88rpx; border-radius: 999rpx;
  display: inline-flex; align-items: center; justify-content: center;
  font-size: 30rpx; letter-spacing: 6rpx;
  box-shadow: 0 10rpx 30rpx rgba(66,132,255,.35), inset 0 0 0 1rpx rgba(255,255,255,.12);
  transition: transform .15s ease, filter .2s ease, opacity .2s ease;
}
.btn.primary {
  background: linear-gradient(135deg, #4a7dff 0%, #8750ff 48%, #c842ff 100%);
  color: #fff;
}
.btn.ghost {
  background: linear-gradient(135deg, #ffb34a 0%, #ff5d5d 48%, #ff47d6 100%);
  color: #fff;
}
.btn:active { transform: scale(.98); filter: brightness(.95); }

@keyframes twinkle {
  0%, 100% { transform: translateY(0) scale(1); opacity: .35; }
  50% { transform: translateY(-6rpx) scale(1.15); opacity: .9; }
}
@keyframes borderFlow { to { filter: hue-rotate(360deg); } }
@keyframes holoSweep {
  0% { opacity: .0; transform: translateX(-30%) rotate(0.0001deg); }
  20% { opacity: .65; }
  50% { opacity: .35; }
  100% { opacity: 0; transform: translateX(40%) rotate(0.0001deg); }
}
</style>

